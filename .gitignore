# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# Virtual Environment
.venv/
venv/
ENV/
env/

# IDE
.vscode/
.idea/
*.swp
*.swo
*~

# OS
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Temporary files
*.tmp
*.temp
*.log
*.bak
*.backup

# Project specific
collected_rules.json
classified_rules.json
test_rules/
demo_*.py
test_*.py
*_research.py
*_report.py
additional_*.list

# Generated rules (optional - uncomment if you don't want to track generated rules)
# rules/rule/*.list
# rules/README.md
