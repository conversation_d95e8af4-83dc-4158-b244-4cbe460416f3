# 本地文件支持功能指南

## 概述

Surge规则收集器现在支持从本地文件读取规则，可以与远程URL数据源混合使用。这个功能让您可以：

- 添加自定义规则到现有分类
- 使用本地维护的规则文件
- 混合使用远程和本地数据源
- 保持规则的版本控制和自定义管理

## 配置方法

### 基本语法

在 `config.yaml` 中，每个数据源可以使用以下两种配置之一：

```yaml
# 远程URL数据源
- url: "https://example.com/rules.list"
  name: "Remote Rules"  # 可选

# 本地文件数据源  
- file: "custom/rules.list"
  name: "Custom Rules"  # 可选
```

### 完整配置示例

```yaml
categories:
  telegram:
    name: "Telegram"
    description: "Telegram相关域名和IP"
    output_file: "telegram.list"
    sources:
      # 远程数据源
      - url: "https://cdn.jsdelivr.net/gh/blackmatrix7/ios_rule_script@master/rule/Surge/Telegram/Telegram.list"
      - url: "https://cdn.jsdelivr.net/gh/ACL4SSR/ACL4SSR@master/Clash/Ruleset/Telegram.list"
      
      # 本地文件数据源
      - file: "custom/telegram.list"
        name: "Custom Telegram Rules"
      - file: "/absolute/path/to/telegram_extra.list"
        name: "Extra Telegram Rules"

  aigc:
    name: "AIGC"
    description: "AI服务规则"
    output_file: "aigc.list"
    sources:
      - url: "https://example.com/ai.list"
      - file: "custom/ai_services.list"
        name: "Custom AI Services"
```

## 文件路径支持

### 相对路径
```yaml
- file: "custom/rules.list"           # 相对于项目根目录
- file: "data/custom/rules.list"      # 支持子目录
```

### 绝对路径
```yaml
- file: "/home/<USER>/rules/custom.list"    # Linux/macOS
- file: "C:\\Users\\<USER>\\rules.list"     # Windows
```

## 本地文件格式

本地文件必须使用标准的Surge规则格式：

```
# NAME: Custom Rules
# UPDATED: 2025-07-09
# DESCRIPTION: 自定义规则

# 域名规则
DOMAIN-SUFFIX,example.com
DOMAIN,api.example.com
DOMAIN-KEYWORD,example

# IP规则
IP-CIDR,*******/32,no-resolve
IP-CIDR6,2001:db8::/32,no-resolve
IP-ASN,13335 // Cloudflare

# 进程规则
PROCESS-NAME,example-app

# 复合规则
AND,((DOMAIN,example.com),(PROCESS-NAME,app))
```

### 支持的特性

- ✅ 注释行（以 `#` 开头）
- ✅ 空行
- ✅ 所有标准Surge规则类型
- ✅ 规则注释（`//` 后的内容）
- ✅ 自动去重处理
- ✅ UTF-8编码

## 实际使用示例

### 1. 创建自定义Telegram规则

创建文件 `custom/telegram.list`：

```
# NAME: Custom Telegram Rules
# DESCRIPTION: 自定义Telegram规则

# 自定义域名
DOMAIN-SUFFIX,t.me
DOMAIN-SUFFIX,telegram.me
DOMAIN-SUFFIX,telegram.org

# 自定义IP段
IP-CIDR,**********/22,no-resolve
IP-CIDR,**********/22,no-resolve

# 自定义ASN
IP-ASN,62014 // Telegram Messenger Inc
IP-ASN,62041 // Telegram Messenger Network

# 自定义进程
PROCESS-NAME,Telegram
PROCESS-NAME,Telegram Desktop
```

### 2. 配置config.yaml

```yaml
telegram:
  name: "Telegram"
  description: "Telegram相关域名和IP"
  output_file: "telegram.list"
  sources:
    - url: "https://cdn.jsdelivr.net/gh/blackmatrix7/ios_rule_script@master/rule/Surge/Telegram/Telegram.list"
    - file: "custom/telegram.list"
      name: "Custom Telegram Rules"
```

### 3. 运行收集器

```bash
python3 main.py run
```

## 功能特性

### 智能去重
- 本地文件规则与远程规则一起进行去重
- 支持基本去重和高级去重
- 保留更完整的规则版本（如带注释的规则）

### 错误处理
- 文件不存在时显示错误信息
- 文件格式错误时跳过无效规则
- 不影响其他数据源的正常处理

### 性能优化
- 本地文件读取速度快
- 与远程下载并行处理
- 支持大文件处理

### 日志记录
```
INFO - 正在读取本地文件: /path/to/custom/rules.list
INFO - 成功读取本地文件: /path/to/custom/rules.list
INFO - 成功处理数据源 Custom Rules: 25 条规则
```

## 最佳实践

### 1. 文件组织
```
project/
├── config.yaml
├── custom/
│   ├── telegram.list
│   ├── ai_services.list
│   └── china_custom.list
└── rules/
    ├── telegram.list
    ├── aigc.list
    └── README.md
```

### 2. 版本控制
- 将 `custom/` 目录加入Git版本控制
- 排除生成的 `rules/` 目录
- 定期备份自定义规则文件

### 3. 规则维护
- 使用有意义的注释
- 定期检查规则有效性
- 保持文件格式一致性

### 4. 测试验证
```bash
# 测试本地文件功能
python3 test_local_files.py

# 运行完整流程
python3 main.py run
```

## 故障排除

### 常见问题

1. **文件不存在**
   ```
   ERROR - 本地文件不存在: custom/rules.list
   ```
   解决：检查文件路径是否正确

2. **文件格式错误**
   ```
   WARNING - 跳过无效规则: invalid-rule-format
   ```
   解决：检查规则格式是否符合Surge标准

3. **编码问题**
   ```
   ERROR - 读取本地文件失败: UnicodeDecodeError
   ```
   解决：确保文件使用UTF-8编码

### 调试技巧

1. 使用测试脚本验证配置
2. 检查日志输出确认文件读取状态
3. 验证生成的规则文件内容

## 总结

本地文件支持功能让Surge规则收集器更加灵活和实用：

- 🔧 **灵活配置**：支持URL和本地文件混合使用
- 📁 **文件管理**：支持相对和绝对路径
- 🔄 **智能处理**：自动去重和错误处理
- 📊 **完整统计**：本地规则包含在统计信息中
- 🚀 **高性能**：并行处理和快速读取

现在您可以轻松地添加自定义规则，同时保持与远程数据源的同步！
