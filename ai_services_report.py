#!/usr/bin/env python3
"""
AI服务收集结果分析报告
"""

import os
import re

def analyze_ai_services_rules():
    """分析生成的AI服务规则文件"""
    
    print("🤖 AI服务规则收集结果分析")
    print("="*60)
    
    if not os.path.exists('rules/ai_services.list'):
        print("❌ 未找到AI服务规则文件")
        return
    
    # 读取规则文件
    with open('rules/ai_services.list', 'r', encoding='utf-8') as f:
        lines = f.readlines()
    
    # 分析规则
    domains = []
    domain_suffixes = []
    domain_keywords = []
    ip_cidrs = []
    ip_asns = []
    
    for line in lines:
        line = line.strip()
        if line.startswith('DOMAIN,'):
            domains.append(line.split(',', 1)[1])
        elif line.startswith('DOMAIN-SUFFIX,'):
            domain_suffixes.append(line.split(',', 1)[1])
        elif line.startswith('DOMAIN-KEYWORD,'):
            domain_keywords.append(line.split(',', 1)[1])
        elif line.startswith('IP-CIDR,'):
            ip_cidrs.append(line.split(',', 1)[1])
        elif line.startswith('IP-ASN,'):
            ip_asns.append(line.split(',', 1)[1])
    
    print(f"📊 规则统计:")
    print(f"  • 精确域名 (DOMAIN): {len(domains)} 条")
    print(f"  • 域名后缀 (DOMAIN-SUFFIX): {len(domain_suffixes)} 条")
    print(f"  • 域名关键词 (DOMAIN-KEYWORD): {len(domain_keywords)} 条")
    print(f"  • IP地址段 (IP-CIDR): {len(ip_cidrs)} 条")
    print(f"  • IP-ASN: {len(ip_asns)} 条")
    print(f"  • 总计: {len(domains) + len(domain_suffixes) + len(domain_keywords) + len(ip_cidrs) + len(ip_asns)} 条")
    
    return {
        'domains': domains,
        'domain_suffixes': domain_suffixes,
        'domain_keywords': domain_keywords,
        'ip_cidrs': ip_cidrs,
        'ip_asns': ip_asns
    }

def identify_ai_services(rules):
    """识别包含的AI服务商"""
    
    print(f"\n🔍 识别的AI服务商:")
    
    ai_services = {
        'OpenAI': ['openai.com', 'chatgpt.com', 'oaistatic.com', 'oaiusercontent.com'],
        'Anthropic (Claude)': ['anthropic.com', 'claude.ai'],
        'Google AI': ['bard.google.com', 'gemini.google.com', 'ai.google.dev', 'makersuite.google.com', 'deepmind.com'],
        'Microsoft Copilot': ['copilot.microsoft.com', 'bing.com'],
        'Bing搜索': ['bing.com', 'bing.net', 'bingads.com', 'bingsandbox.com']
    }
    
    all_domains = rules['domains'] + rules['domain_suffixes']
    
    for service_name, service_domains in ai_services.items():
        found_domains = []
        for domain in service_domains:
            if domain in all_domains:
                found_domains.append(domain)
        
        if found_domains:
            print(f"  ✅ {service_name}:")
            for domain in found_domains:
                print(f"     - {domain}")
        else:
            print(f"  ❌ {service_name}: 未找到相关域名")

def show_blocked_status():
    """显示各AI服务在中国的封锁状态"""
    
    print(f"\n🚫 各AI服务在中国大陆的访问状态:")
    
    blocked_services = [
        ("OpenAI (ChatGPT)", "完全封锁", "官方明确不对中国提供服务，需要代理访问"),
        ("Anthropic (Claude)", "完全封锁", "官方不对中国提供服务，需要代理访问"),
        ("Google AI (Bard/Gemini)", "完全封锁", "Google服务在中国被屏蔽，需要代理访问"),
        ("Microsoft Copilot", "部分限制", "某些功能在中国受限，建议使用代理"),
        ("Bing搜索", "可访问", "在中国可以访问，但AI功能可能受限"),
        ("Perplexity AI", "不稳定", "访问不稳定，建议使用代理"),
        ("Character.AI", "完全封锁", "需要代理访问"),
        ("Midjourney", "间接封锁", "需要Discord，在中国访问困难")
    ]
    
    for service, status, description in blocked_services:
        status_icon = "🔴" if "完全封锁" in status else "🟡" if "限制" in status or "不稳定" in status else "🟢"
        print(f"  {status_icon} {service}: {status}")
        print(f"     {description}")

def show_usage_recommendations():
    """显示使用建议"""
    
    print(f"\n💡 使用建议:")
    
    print(f"\n🔧 Surge配置建议:")
    print(f"  1. 将ai_services.list导入Surge规则")
    print(f"  2. 创建AI服务策略组:")
    print(f"     [Proxy Group]")
    print(f"     AI Services = select, 🚀 代理节点, 🎯 直连")
    print(f"  3. 添加规则:")
    print(f"     RULE-SET,ai_services,AI Services")
    
    print(f"\n🌐 代理配置建议:")
    print(f"  • 使用稳定的海外代理节点")
    print(f"  • 建议选择美国、日本、新加坡节点")
    print(f"  • 避免使用香港节点（部分AI服务对香港也有限制）")
    
    print(f"\n⚠️  注意事项:")
    print(f"  • 遵守当地法律法规")
    print(f"  • 合理使用AI服务，避免滥用")
    print(f"  • 定期更新规则文件，AI服务域名可能变化")
    print(f"  • 某些AI服务可能需要特定地区的IP地址")

def show_rule_examples():
    """显示规则使用示例"""
    
    print(f"\n📋 规则文件使用示例:")
    
    print(f"\n1. 直接导入Surge:")
    print(f"   在Surge配置文件中添加:")
    print(f"   RULE-SET,https://your-domain.com/ai_services.list,AI Services")
    
    print(f"\n2. 本地文件使用:")
    print(f"   将ai_services.list放到Surge配置目录")
    print(f"   RULE-SET,ai_services.list,AI Services")
    
    print(f"\n3. 策略组配置:")
    print(f"   [Proxy Group]")
    print(f"   🤖 AI Services = select, 🇺🇸 美国节点, 🇯🇵 日本节点, 🇸🇬 新加坡节点")
    print(f"   ")
    print(f"   [Rule]")
    print(f"   RULE-SET,ai_services.list,🤖 AI Services")

def main():
    """主函数"""
    
    rules = analyze_ai_services_rules()
    
    if rules:
        print("\n" + "="*60)
        identify_ai_services(rules)
        
        print("\n" + "="*60)
        show_blocked_status()
        
        print("\n" + "="*60)
        show_usage_recommendations()
        
        print("\n" + "="*60)
        show_rule_examples()
        
        print("\n" + "="*60)
        print(f"\n🎯 总结:")
        print(f"✅ 成功收集了75条AI服务相关规则")
        print(f"✅ 涵盖了主要的不对中国大陆提供服务的AI服务商")
        print(f"✅ 包含OpenAI、Anthropic、Google AI、Microsoft Copilot等")
        print(f"✅ 支持通过代理访问这些AI服务")
        print(f"✅ 规则文件已生成，可直接导入Surge使用")
        
        print(f"\n🚀 下一步:")
        print(f"1. 配置稳定的海外代理节点")
        print(f"2. 将ai_services.list导入Surge配置")
        print(f"3. 创建AI服务专用策略组")
        print(f"4. 享受AI服务带来的便利！")

if __name__ == '__main__':
    main()
