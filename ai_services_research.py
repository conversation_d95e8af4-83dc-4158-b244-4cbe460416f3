#!/usr/bin/env python3
"""
AI服务域名研究和整理脚本
专门收集不对中国大陆地区提供服务的AI服务商域名
"""

def get_blocked_ai_services():
    """获取不对中国大陆提供服务的AI服务商列表"""
    
    ai_services = {
        "大语言模型服务": {
            "OpenAI": [
                "openai.com",
                "chatgpt.com", 
                "oaistatic.com",
                "oaiusercontent.com",
                "openaiapi-site.azureedge.net"
            ],
            "<PERSON><PERSON><PERSON> (<PERSON>)": [
                "anthropic.com",
                "claude.ai"
            ],
            "Google AI (Gemini/Bard)": [
                "bard.google.com",
                "gemini.google.com", 
                "ai.google.dev",
                "makersuite.google.com",
                "aistudio.google.com"
            ],
            "Microsoft Copilot": [
                "copilot.microsoft.com",
                "bing.com",
                "sydney.bing.com"
            ],
            "Perplexity AI": [
                "perplexity.ai",
                "pplx.ai"
            ],
            "Character.AI": [
                "character.ai",
                "characterai.io"
            ],
            "Cohere": [
                "cohere.ai",
                "cohere.com"
            ]
        },
        
        "图像生成服务": {
            "Midjourney": [
                "midjourney.com",
                "cdn.midjourney.com"
            ],
            "Stability AI": [
                "stability.ai",
                "dreamstudio.ai",
                "clipdrop.co"
            ],
            "DALL-E": [
                "labs.openai.com"
            ],
            "Leonardo AI": [
                "leonardo.ai"
            ],
            "Ideogram": [
                "ideogram.ai"
            ],
            "Playground AI": [
                "playgroundai.com"
            ],
            "NightCafe": [
                "nightcafe.studio"
            ],
            "Artbreeder": [
                "artbreeder.com"
            ],
            "Starryai": [
                "starryai.com"
            ],
            "Craiyon": [
                "craiyon.com"
            ]
        },
        
        "视频生成服务": {
            "Runway ML": [
                "runwayml.com"
            ],
            "Pika Labs": [
                "pika.art"
            ],
            "Luma AI": [
                "lumalabs.ai"
            ],
            "Synthesia": [
                "synthesia.io"
            ],
            "Hour One": [
                "hourone.ai"
            ],
            "D-ID": [
                "d-id.com"
            ]
        },
        
        "音频生成服务": {
            "ElevenLabs": [
                "elevenlabs.io"
            ],
            "Murf AI": [
                "murf.ai"
            ],
            "Speechify": [
                "speechify.com"
            ],
            "Descript": [
                "descript.com"
            ]
        },
        
        "写作辅助服务": {
            "Jasper AI": [
                "jasper.ai"
            ],
            "Copy.ai": [
                "copy.ai"
            ],
            "Writesonic": [
                "writesonic.com"
            ],
            "Grammarly": [
                "grammarly.com"
            ],
            "Notion AI": [
                "notion.so",
                "notion.com"
            ],
            "Wordtune": [
                "wordtune.com"
            ],
            "Quillbot": [
                "quillbot.com"
            ],
            "Rytr": [
                "rytr.me"
            ]
        },
        
        "开发平台服务": {
            "Hugging Face": [
                "huggingface.co",
                "hf.co"
            ],
            "Replicate": [
                "replicate.com"
            ],
            "Gradio": [
                "gradio.app"
            ]
        },
        
        "设计工具服务": {
            "Gamma": [
                "gamma.app"
            ],
            "Tome": [
                "tome.app"
            ],
            "Beautiful.AI": [
                "beautiful.ai"
            ],
            "Canva AI": [
                "canva.com"
            ],
            "Figma AI": [
                "figma.com"
            ]
        }
    }
    
    return ai_services

def generate_surge_rules():
    """生成Surge规则格式"""
    
    ai_services = get_blocked_ai_services()
    rules = []
    
    # 添加文件头
    rules.append("# NAME: AI Services (Blocked in China)")
    rules.append("# AUTHOR: SurgeRuleCollector")
    rules.append("# DESCRIPTION: 不对中国大陆地区提供服务的AI服务商域名")
    rules.append("# UPDATED: 2025-07-09")
    rules.append("")
    
    # 按分类生成规则
    for category, services in ai_services.items():
        rules.append(f"# {category}")
        
        for service_name, domains in services.items():
            rules.append(f"# {service_name}")
            for domain in domains:
                rules.append(f"DOMAIN-SUFFIX,{domain}")
            rules.append("")
        
        rules.append("")
    
    return rules

def analyze_ai_services():
    """分析AI服务统计信息"""
    
    ai_services = get_blocked_ai_services()
    
    print("🤖 不对中国大陆提供服务的AI服务商分析")
    print("="*60)
    
    total_services = 0
    total_domains = 0
    
    for category, services in ai_services.items():
        category_services = len(services)
        category_domains = sum(len(domains) for domains in services.values())
        
        total_services += category_services
        total_domains += category_domains
        
        print(f"\n📂 {category}")
        print(f"   服务商数量: {category_services}")
        print(f"   域名数量: {category_domains}")
        
        for service_name, domains in services.items():
            print(f"   • {service_name}: {len(domains)} 个域名")
            for domain in domains[:3]:  # 显示前3个域名
                print(f"     - {domain}")
            if len(domains) > 3:
                print(f"     ... 还有 {len(domains) - 3} 个域名")
    
    print(f"\n📊 总体统计:")
    print(f"   总服务商数量: {total_services}")
    print(f"   总域名数量: {total_domains}")
    print(f"   平均每个服务商: {total_domains/total_services:.1f} 个域名")

def show_access_status():
    """显示各服务在中国的访问状态"""
    
    print("\n🌐 各AI服务在中国大陆的访问状态:")
    print("="*60)
    
    blocked_reasons = {
        "OpenAI": "官方明确不对中国提供服务",
        "Anthropic": "官方明确不对中国提供服务", 
        "Google AI": "Google服务在中国被屏蔽",
        "Microsoft Copilot": "部分功能在中国受限",
        "Midjourney": "需要Discord，在中国访问困难",
        "Character.AI": "官方不对中国提供服务",
        "Stability AI": "部分服务在中国无法访问",
        "Hugging Face": "访问不稳定，经常被屏蔽"
    }
    
    for service, reason in blocked_reasons.items():
        print(f"🚫 {service}: {reason}")

def main():
    """主函数"""
    
    analyze_ai_services()
    show_access_status()
    
    print("\n" + "="*60)
    print("\n💡 建议:")
    print("1. 🔧 配置代理服务器访问这些AI服务")
    print("2. 📋 将这些域名添加到Surge分流规则中")
    print("3. 🌐 使用代理策略组访问AI服务")
    print("4. 🔄 定期更新域名列表，因为AI服务发展迅速")
    
    print("\n🚀 使用方法:")
    print("1. 运行 python3 main.py run 收集最新规则")
    print("2. 查看生成的 ai_services.list 文件")
    print("3. 将规则导入Surge配置文件")
    print("4. 配置代理策略组访问AI服务")
    
    # 生成规则文件
    rules = generate_surge_rules()
    
    print(f"\n📝 生成了 {len([r for r in rules if r.startswith('DOMAIN')])} 条域名规则")
    print("规则已保存到内存，可以通过程序输出查看")
    
    # 显示前20条规则作为示例
    print("\n📋 规则示例 (前20条):")
    domain_rules = [r for r in rules if r.startswith('DOMAIN')][:20]
    for i, rule in enumerate(domain_rules, 1):
        print(f"  {i:2d}. {rule}")
    
    if len(domain_rules) > 20:
        print(f"  ... 还有 {len([r for r in rules if r.startswith('DOMAIN')]) - 20} 条规则")

if __name__ == '__main__':
    main()
