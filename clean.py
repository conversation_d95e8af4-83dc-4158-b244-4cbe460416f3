#!/usr/bin/env python3
"""
清理脚本 - 清理项目中的临时文件和缓存
"""

import os
import shutil
import glob

def clean_pycache():
    """清理Python缓存文件"""
    print("🧹 清理Python缓存文件...")
    
    # 查找所有__pycache__目录
    pycache_dirs = []
    for root, dirs, files in os.walk('.'):
        if '__pycache__' in dirs:
            pycache_path = os.path.join(root, '__pycache__')
            # 跳过虚拟环境中的缓存
            if '.venv' not in pycache_path and 'venv' not in pycache_path:
                pycache_dirs.append(pycache_path)
    
    for pycache_dir in pycache_dirs:
        try:
            shutil.rmtree(pycache_dir)
            print(f"  ✅ 删除: {pycache_dir}")
        except Exception as e:
            print(f"  ❌ 删除失败: {pycache_dir} - {e}")
    
    # 清理.pyc文件
    pyc_files = glob.glob('**/*.pyc', recursive=True)
    for pyc_file in pyc_files:
        if '.venv' not in pyc_file and 'venv' not in pyc_file:
            try:
                os.remove(pyc_file)
                print(f"  ✅ 删除: {pyc_file}")
            except Exception as e:
                print(f"  ❌ 删除失败: {pyc_file} - {e}")

def clean_temp_files():
    """清理临时文件"""
    print("🧹 清理临时文件...")
    
    temp_patterns = [
        '*.tmp',
        '*.temp',
        '*.log',
        '*.bak',
        '*.backup',
        '*~',
        'collected_rules.json',
        'classified_rules.json'
    ]
    
    for pattern in temp_patterns:
        files = glob.glob(pattern)
        for file in files:
            try:
                os.remove(file)
                print(f"  ✅ 删除: {file}")
            except Exception as e:
                print(f"  ❌ 删除失败: {file} - {e}")

def clean_test_files():
    """清理测试和演示文件"""
    print("🧹 清理测试和演示文件...")
    
    test_patterns = [
        'demo_*.py',
        'test_*.py',
        '*_test.py',
        '*_demo.py',
        'test_rules/',
        'demo_rules/'
    ]
    
    for pattern in test_patterns:
        # 保留核心测试文件
        if pattern in ['tests/test_basic.py']:
            continue
            
        files = glob.glob(pattern)
        for file in files:
            # 保留tests目录下的文件
            if file.startswith('tests/'):
                continue
                
            try:
                if os.path.isfile(file):
                    os.remove(file)
                    print(f"  ✅ 删除文件: {file}")
                elif os.path.isdir(file):
                    shutil.rmtree(file)
                    print(f"  ✅ 删除目录: {file}")
            except Exception as e:
                print(f"  ❌ 删除失败: {file} - {e}")

def clean_os_files():
    """清理操作系统生成的文件"""
    print("🧹 清理操作系统文件...")
    
    os_patterns = [
        '.DS_Store',
        '.DS_Store?',
        '._*',
        '.Spotlight-V100',
        '.Trashes',
        'ehthumbs.db',
        'Thumbs.db'
    ]
    
    for pattern in os_patterns:
        files = glob.glob(f'**/{pattern}', recursive=True)
        for file in files:
            # 跳过.git和.venv目录
            if '.git' in file or '.venv' in file:
                continue
                
            try:
                os.remove(file)
                print(f"  ✅ 删除: {file}")
            except Exception as e:
                print(f"  ❌ 删除失败: {file} - {e}")

def show_summary():
    """显示清理后的目录结构"""
    print("\n📊 清理后的项目结构:")
    print("="*50)
    
    # 显示主要文件和目录
    items = []
    for item in os.listdir('.'):
        if not item.startswith('.') and item not in ['__pycache__']:
            if os.path.isdir(item):
                items.append(f"📁 {item}/")
            else:
                items.append(f"📄 {item}")
    
    for item in sorted(items):
        print(f"  {item}")

def main():
    """主函数"""
    print("🚀 开始清理项目...")
    print("="*50)
    
    # 执行各种清理操作
    clean_pycache()
    print()
    clean_temp_files()
    print()
    clean_os_files()
    print()
    
    # 显示清理结果
    show_summary()
    
    print("\n✅ 清理完成!")
    print("\n💡 提示:")
    print("  - 临时文件已清理")
    print("  - Python缓存已清理")
    print("  - 操作系统文件已清理")
    print("  - 核心项目文件已保留")

if __name__ == '__main__':
    main()
