# Surge规则收集器配置文件

# 数据源配置 - 按分类组织多个数据源
sources:

# 分类配置 - 每个分类包含多个数据源
categories:
  china_asn:
    name: "China IP-ASN"
    description: "中国大陆IP地址段和ASN"
    output_file: "rule/cn_asn.list"
    sources:
      - url: https://cdn.jsdelivr.net/gh/blackmatrix7/ios_rule_script@master/rule/Surge/ChinaASN/ChinaASN.list
      - url: https://raw.githubusercontent.com/missuo/ASN-China/main/ASN.China.list
      - url: https://raw.githubusercontent.com/VirgilClyne/GetSomeFries/main/ruleset/ASN.China.list

  china_sites:
    name: "China Domains"
    description: "中国大陆常见域名"
    output_file: "rule/cn_sites.list"
    sources:
      - url: https://raw.githubusercontent.com/blackmatrix7/ios_rule_script/refs/heads/master/rule/Surge/China/China_All.list
      - url: https://cdn.jsdelivr.net/gh/Loyalsoldier/surge-rules@release/direct.txt

  foreign_domains:
    name: "proxy sites"
    description: "国外常见网站"
    output_file: "rule/proxy.list"
    sources:
      - file: custom/proxy.list
      - url: https://whatshub.top/rule/Proxy.list
      - url: https://cdn.jsdelivr.net/gh/Loyalsoldier/surge-rules@release/ruleset/proxy.txt

  telegram:
    name: "Telegram"
    description: "Telegram相关域名和IP"
    output_file: "rule/telegram.list"
    sources:
      - url: https://cdn.jsdelivr.net/gh/Loyalsoldier/surge-rules@release/telegramcidr.txt
      - file: "custom/telegram.list"

  aigc:
    name: "AIGC"
    description: "不对中国大陆地区提供服务的AI服务商域名和IP"
    output_file: "rule/aigc.list"
    sources:
      - url: https://raw.githubusercontent.com/yzj160212/Surge/refs/heads/main/OpenAI.list
      - url: https://whatshub.top/rule/ai.list
      - file: "custom/ai_services.list"

  direct:
    name: "Direct"
    description: "无需代理的域名"
    output_file: "rule/direct.list"
    sources:
      - url: https://raw.githubusercontent.com/blackmatrix7/ios_rule_script/master/rule/Surge/Direct/Direct.list

# 输出配置
output:
  directory: "rules"
  format: "surge"
  include_stats: true
  include_header: true

# 去重配置
deduplication:
  enable_basic: true # 启用基本去重（移除完全相同的规则）
  enable_advanced: true # 启用高级去重（移除冗余规则）
  enable_collection_dedup: true # 在收集阶段进行去重
  enable_classification_dedup: true # 在分类阶段进行去重
  enable_generation_dedup: true # 在生成阶段进行最终去重

# 代理配置
proxy:
  # 全局代理设置（可选）
  http_proxy: "http://127.0.0.1:6152"
  https_proxy: "http://127.0.0.1:6152"

  # 需要使用代理的域名列表
  proxy_domains:
    - "github.com"
    - "githubusercontent.com"
    - "raw.githubusercontent.com"
    - "cdn.jsdelivr.net"

  # 不使用代理的域名列表（优先级高于proxy_domains）
  no_proxy_domains:
    - "localhost"
    - "127.0.0.1"

# 其他配置
settings:
  user_agent: "SurgeRuleCollector/1.0"
  timeout: 30
  retry_count: 3
  concurrent_downloads: 5
