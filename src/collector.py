"""
Surge规则收集器模块
负责从各种数据源收集Surge规则
"""

import requests
import time
import logging
import os
from typing import List, Dict, Optional, Tuple
from concurrent.futures import ThreadPoolExecutor, as_completed
from urllib.parse import urljoin, urlparse
import re
try:
    from .deduplicator import RuleDeduplicator
except ImportError:
    from deduplicator import RuleDeduplicator


class RuleCollector:
    """规则收集器类"""
    
    def __init__(self, config: Dict):
        self.config = config
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': config.get('settings', {}).get('user_agent', 'SurgeRuleCollector/1.0')
        })
        self.timeout = config.get('settings', {}).get('timeout', 30)
        self.retry_count = config.get('settings', {}).get('retry_count', 3)
        self.concurrent_downloads = config.get('settings', {}).get('concurrent_downloads', 5)

        # 设置日志
        logging.basicConfig(level=logging.INFO)
        self.logger = logging.getLogger(__name__)

        # 代理配置
        self.proxy_config = config.get('proxy', {})
        self.setup_proxy()

        # 初始化去重器
        self.deduplicator = RuleDeduplicator()
        self.dedup_config = config.get('deduplication', {})

    def setup_proxy(self):
        """设置代理配置"""
        # 获取代理设置
        http_proxy = self.proxy_config.get('http_proxy')
        https_proxy = self.proxy_config.get('https_proxy')

        # 如果配置了全局代理，设置为默认代理
        if http_proxy or https_proxy:
            proxies = {}
            if http_proxy:
                proxies['http'] = http_proxy
            if https_proxy:
                proxies['https'] = https_proxy

            # 设置默认代理（可能会被特定URL的代理设置覆盖）
            self.default_proxies = proxies
            self.logger.info(f"配置默认代理: {proxies}")
        else:
            self.default_proxies = None

    def should_use_proxy(self, url: str) -> bool:
        """
        判断指定URL是否应该使用代理

        Args:
            url: 要访问的URL

        Returns:
            是否使用代理
        """
        parsed_url = urlparse(url)
        domain = parsed_url.netloc.lower()

        # 检查不使用代理的域名列表（优先级最高）
        no_proxy_domains = self.proxy_config.get('no_proxy_domains', [])
        for no_proxy_domain in no_proxy_domains:
            if domain == no_proxy_domain.lower() or domain.endswith('.' + no_proxy_domain.lower()):
                return False

        # 检查需要使用代理的域名列表
        proxy_domains = self.proxy_config.get('proxy_domains', [])
        if proxy_domains:
            for proxy_domain in proxy_domains:
                if domain == proxy_domain.lower() or domain.endswith('.' + proxy_domain.lower()):
                    return True
            # 如果配置了proxy_domains但URL不在列表中，不使用代理
            return False

        # 如果没有配置proxy_domains但配置了全局代理，使用代理
        return self.default_proxies is not None

    def get_proxies_for_url(self, url: str) -> Optional[Dict[str, str]]:
        """
        获取指定URL的代理设置

        Args:
            url: 要访问的URL

        Returns:
            代理设置字典，如果不使用代理则返回None
        """
        if self.should_use_proxy(url):
            return self.default_proxies
        return None
    
    def fetch_rule_content(self, url: str) -> Optional[str]:
        """
        获取规则文件内容

        Args:
            url: 规则文件URL

        Returns:
            规则文件内容，失败返回None
        """
        # 获取代理设置
        proxies = self.get_proxies_for_url(url)
        proxy_info = f" (通过代理: {proxies})" if proxies else " (直连)"

        for attempt in range(self.retry_count):
            try:
                self.logger.info(f"正在获取规则: {url}{proxy_info} (尝试 {attempt + 1}/{self.retry_count})")
                response = self.session.get(url, timeout=self.timeout, proxies=proxies)
                response.raise_for_status()

                content = response.text.strip()
                if content:
                    self.logger.info(f"成功获取规则: {url}")
                    return content
                else:
                    self.logger.warning(f"规则文件为空: {url}")

            except requests.exceptions.RequestException as e:
                self.logger.warning(f"获取规则失败 (尝试 {attempt + 1}/{self.retry_count}): {url} - {e}")
                if attempt < self.retry_count - 1:
                    time.sleep(2 ** attempt)  # 指数退避

        self.logger.error(f"无法获取规则: {url}")
        return None

    def fetch_local_file_content(self, file_path: str) -> Optional[str]:
        """
        读取本地规则文件内容

        Args:
            file_path: 本地文件路径

        Returns:
            规则文件内容，失败返回None
        """
        try:
            # 如果是相对路径，相对于当前工作目录
            if not os.path.isabs(file_path):
                file_path = os.path.abspath(file_path)

            self.logger.info(f"正在读取本地文件: {file_path}")

            if not os.path.exists(file_path):
                self.logger.error(f"本地文件不存在: {file_path}")
                return None

            if not os.path.isfile(file_path):
                self.logger.error(f"路径不是文件: {file_path}")
                return None

            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read().strip()

            if content:
                self.logger.info(f"成功读取本地文件: {file_path}")
                return content
            else:
                self.logger.warning(f"本地文件为空: {file_path}")
                return None

        except Exception as e:
            self.logger.error(f"读取本地文件失败: {file_path} - {e}")
            return None

    def fetch_rule_content_from_source(self, source_config: Dict) -> Optional[str]:
        """
        从数据源获取规则内容（支持URL和本地文件）

        Args:
            source_config: 数据源配置

        Returns:
            规则文件内容，失败返回None
        """
        # 检查是否为本地文件
        if 'file' in source_config:
            return self.fetch_local_file_content(source_config['file'])
        # 检查是否为URL
        elif 'url' in source_config:
            return self.fetch_rule_content(source_config['url'])
        else:
            self.logger.error(f"数据源配置无效，必须包含 'url' 或 'file' 字段: {source_config}")
            return None

    def parse_rule_content(self, content: str) -> Tuple[Dict[str, int], List[str]]:
        """
        解析规则文件内容
        
        Args:
            content: 规则文件内容
            
        Returns:
            (统计信息, 规则列表)
        """
        lines = content.split('\n')
        rules = []
        stats = {}
        
        for line in lines:
            line = line.strip()
            
            # 跳过注释和空行
            if not line or line.startswith('#'):
                # 解析统计信息
                if line.startswith('# ') and ':' in line:
                    parts = line[2:].split(':', 1)
                    if len(parts) == 2:
                        key = parts[0].strip()
                        try:
                            value = int(parts[1].strip())
                            stats[key] = value
                        except ValueError:
                            pass
                continue
            
            # 验证规则格式
            if self._is_valid_rule(line):
                rules.append(line)

        # 对单个文件的规则进行基本去重（如果启用）
        if rules and self.dedup_config.get('enable_collection_dedup', True):
            deduplicated_rules, dedup_stats = self.deduplicator.deduplicate_rules(
                rules, enable_advanced=False  # 在收集阶段只做基本去重
            )
            if dedup_stats['removed'] > 0:
                self.logger.info(f"文件内去重: 移除了 {dedup_stats['removed']} 条重复规则")
            rules = deduplicated_rules

        return stats, rules
    
    def _is_valid_rule(self, rule: str) -> bool:
        """
        验证规则格式是否正确
        
        Args:
            rule: 规则字符串
            
        Returns:
            是否为有效规则
        """
        # Surge规则类型
        valid_types = [
            'DOMAIN', 'DOMAIN-SUFFIX', 'DOMAIN-KEYWORD',
            'IP-CIDR', 'IP-CIDR6', 'IP-ASN',
            'PROCESS-NAME', 'USER-AGENT',
            'URL-REGEX', 'AND', 'OR', 'NOT'
        ]
        
        # 检查规则是否以有效类型开头
        for rule_type in valid_types:
            if rule.startswith(rule_type + ','):
                return True
        
        return False
    
    def collect_from_category_sources(self, category_name: str, sources_config: List[Dict]) -> Tuple[Dict, List[str]]:
        """
        从指定分类的多个数据源收集规则并汇总

        Args:
            category_name: 分类名称
            sources_config: 数据源配置列表

        Returns:
            (汇总统计信息, 汇总规则列表)
        """
        all_rules = []
        all_stats = {}

        # 使用线程池并发处理（URL下载）和串行处理（本地文件读取）
        with ThreadPoolExecutor(max_workers=self.concurrent_downloads) as executor:
            # 提交所有任务
            future_to_source = {}
            for source_config in sources_config:
                # 检查数据源配置是否有效
                if not ('url' in source_config or 'file' in source_config):
                    self.logger.warning(f"数据源配置无效，跳过: {source_config}")
                    continue

                future = executor.submit(self.fetch_rule_content_from_source, source_config)
                future_to_source[future] = source_config

            # 处理完成的任务
            for future in as_completed(future_to_source):
                source_config = future_to_source[future]
                # 获取数据源路径（URL或文件路径）
                source_path = source_config.get('url') or source_config.get('file')
                # 生成显示名称：优先使用配置的name，否则从路径提取
                source_name = source_config.get('name') or self._extract_source_name(source_path)

                try:
                    content = future.result()
                    if content:
                        stats, rules = self.parse_rule_content(content)
                        all_rules.extend(rules)

                        # 合并统计信息
                        for rule_type, count in stats.items():
                            all_stats[rule_type] = all_stats.get(rule_type, 0) + count

                        self.logger.info(f"成功处理数据源 {source_name}: {len(rules)} 条规则")
                    else:
                        self.logger.warning(f"数据源 {source_name} 内容为空")

                except Exception as e:
                    self.logger.error(f"处理数据源 {source_name} 时出错: {e}")

        # 对汇总的规则进行去重（如果启用）
        if all_rules and self.dedup_config.get('enable_collection_dedup', True):
            deduplicated_rules, dedup_stats = self.deduplicator.deduplicate_rules(
                all_rules, enable_advanced=False  # 在收集阶段只做基本去重
            )
            if dedup_stats['removed'] > 0:
                self.logger.info(f"分类 {category_name} 汇总去重: 移除了 {dedup_stats['removed']} 条重复规则")
            all_rules = deduplicated_rules

        # 更新统计信息
        if all_rules:
            final_stats = {}
            for rule in all_rules:
                rule_type = rule.split(',')[0]
                final_stats[rule_type] = final_stats.get(rule_type, 0) + 1
            final_stats['TOTAL'] = len(all_rules)
            all_stats = final_stats

        return all_stats, all_rules

    def _extract_source_name(self, source_path: str) -> str:
        """
        从URL或文件路径中提取简短的显示名称

        Args:
            source_path: 数据源URL或文件路径

        Returns:
            简短的显示名称
        """
        # 如果是本地文件路径
        if not source_path.startswith(('http://', 'https://')):
            return os.path.basename(source_path)

        # 如果是URL
        try:
            from urllib.parse import urlparse
            parsed = urlparse(source_path)
            domain = parsed.netloc

            # 提取域名的主要部分
            if domain.startswith('www.'):
                domain = domain[4:]

            # 如果是GitHub相关的URL，尝试提取仓库信息
            if 'github' in domain or 'githubusercontent' in domain:
                path_parts = parsed.path.strip('/').split('/')
                if len(path_parts) >= 2:
                    return f"{path_parts[0]}/{path_parts[1]}"

            # 如果是CDN，尝试提取项目信息
            if 'cdn.jsdelivr.net' in domain:
                path_parts = parsed.path.strip('/').split('/')
                if len(path_parts) >= 3 and path_parts[0] == 'gh':
                    return f"{path_parts[1]}/{path_parts[2]}"

            return domain
        except Exception:
            # 如果解析失败，返回URL的最后部分
            return source_path.split('/')[-1] or source_path

    def collect_all_rules(self) -> Dict[str, Tuple[Dict, List[str]]]:
        """
        收集所有配置的规则（按分类组织）

        Returns:
            {分类名称: (统计信息, 规则列表)}
        """
        all_results = {}
        categories = self.config.get('categories', {})

        for category_name, category_config in categories.items():
            sources_config = category_config.get('sources', [])
            if not sources_config:
                self.logger.warning(f"分类 {category_name} 没有配置数据源，跳过")
                continue

            self.logger.info(f"开始收集分类: {category_name} (来源: {len(sources_config)} 个)")
            stats, rules = self.collect_from_category_sources(category_name, sources_config)
            all_results[category_name] = (stats, rules)
            self.logger.info(f"完成分类 {category_name}: 收集了 {len(rules)} 条规则")

        return all_results
