"""
Surge规则文件生成器模块
负责生成标准格式的Surge规则文件
"""

import os
import logging
from datetime import datetime
from typing import Dict, List
from collections import defaultdict
try:
    from .deduplicator import RuleDeduplicator
except ImportError:
    from deduplicator import RuleDeduplicator


class RuleFileGenerator:
    """规则文件生成器类"""
    
    def __init__(self, config: Dict):
        self.config = config
        self.output_config = config.get('output', {})
        self.categories = config.get('categories', {})
        
        # 设置日志
        self.logger = logging.getLogger(__name__)

        # 初始化去重器
        self.deduplicator = RuleDeduplicator()
        self.dedup_config = config.get('deduplication', {})

        # 确保输出目录存在
        self.output_dir = self.output_config.get('directory', 'rules')
        os.makedirs(self.output_dir, exist_ok=True)
    
    def generate_file_header(self, category_name: str, category_config: Dict, stats: Dict[str, int]) -> str:
        """
        生成规则文件头部信息

        Args:
            category_name: 分类名称
            category_config: 分类配置
            stats: 统计信息

        Returns:
            文件头部字符串
        """
        header_lines = []

        # 基本信息
        header_lines.append(f"# NAME: {category_config.get('name', category_name)}")
        header_lines.append(f"# UPDATED: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")

        # 统计信息
        for rule_type, count in sorted(stats.items()):
            if rule_type != 'TOTAL':
                header_lines.append(f"# {rule_type}: {count}")

        # 总计
        total = stats.get('TOTAL', 0)
        header_lines.append(f"# TOTAL: {total}")

        return '\n'.join(header_lines)
    
    def sort_rules(self, rules: List[str]) -> List[str]:
        """
        对规则进行排序
        
        Args:
            rules: 规则列表
            
        Returns:
            排序后的规则列表
        """
        # 定义规则类型的优先级
        type_priority = {
            'DOMAIN': 1,
            'DOMAIN-SUFFIX': 2,
            'DOMAIN-KEYWORD': 3,
            'IP-CIDR': 4,
            'IP-CIDR6': 5,
            'IP-ASN': 6,
            'PROCESS-NAME': 7,
            'USER-AGENT': 8,
            'URL-REGEX': 9,
            'AND': 10,
            'OR': 11,
            'NOT': 12
        }
        
        def get_sort_key(rule: str) -> tuple:
            """获取规则的排序键"""
            rule_type = rule.split(',')[0]
            priority = type_priority.get(rule_type, 999)
            
            # 在同类型内按字母顺序排序
            return (priority, rule.lower())
        
        return sorted(rules, key=get_sort_key)
    
    def generate_rule_file(self, category_name: str, rules: List[str], stats: Dict[str, int]) -> str:
        """
        生成单个规则文件内容
        
        Args:
            category_name: 分类名称
            rules: 规则列表
            stats: 统计信息
            
        Returns:
            文件内容
        """
        content_lines = []
        
        # 添加文件头部
        if self.output_config.get('include_header', True):
            category_config = self.categories.get(category_name, {})
            header = self.generate_file_header(category_name, category_config, stats)
            content_lines.append(header)
            content_lines.append('')  # 空行分隔
        
        # 添加规则
        if rules:
            sorted_rules = self.sort_rules(rules)
            content_lines.extend(sorted_rules)
        
        return '\n'.join(content_lines)
    
    def calculate_stats(self, rules: List[str]) -> Dict[str, int]:
        """
        计算规则统计信息
        
        Args:
            rules: 规则列表
            
        Returns:
            统计信息字典
        """
        stats = defaultdict(int)
        
        for rule in rules:
            rule_type = rule.split(',')[0]
            stats[rule_type] += 1
        
        stats['TOTAL'] = len(rules)
        return dict(stats)
    
    def generate_all_files(self, classified_rules: Dict[str, List[str]]) -> Dict[str, str]:
        """
        生成所有分类的规则文件
        
        Args:
            classified_rules: 分类后的规则
            
        Returns:
            {分类名称: 文件路径}
        """
        generated_files = {}
        
        for category_name, rules in classified_rules.items():
            if not rules:
                self.logger.warning(f"分类 {category_name} 没有规则，跳过生成")
                continue
            
            # 获取输出文件名
            category_config = self.categories.get(category_name, {})
            filename = category_config.get('output_file', f"{category_name}.list")
            filepath = os.path.join(self.output_dir, filename)
            
            # 最终去重确认（防止分类过程中产生的重复）
            if self.dedup_config.get('enable_generation_dedup', True):
                final_rules, dedup_stats = self.deduplicator.deduplicate_rules(
                    rules, enable_advanced=False  # 生成阶段只做基本去重，避免过度处理
                )
                if dedup_stats['removed'] > 0:
                    self.logger.info(f"分类 {category_name} 最终去重: 移除了 {dedup_stats['removed']} 条重复规则")
            else:
                final_rules = rules

            # 计算统计信息
            stats = self.calculate_stats(final_rules)

            # 生成文件内容
            content = self.generate_rule_file(category_name, final_rules, stats)
            
            # 写入文件
            try:
                with open(filepath, 'w', encoding='utf-8') as f:
                    f.write(content)
                
                generated_files[category_name] = filepath
                self.logger.info(f"生成规则文件: {filepath} ({len(final_rules)} 条规则)")
                
            except Exception as e:
                self.logger.error(f"生成文件 {filepath} 时出错: {e}")
        
        return generated_files
    
    def generate_summary_file(self, classified_rules: Dict[str, List[str]], generated_files: Dict[str, str]):
        """
        生成汇总文件

        Args:
            classified_rules: 分类后的规则
            generated_files: 生成的文件路径
        """
        summary_lines = []

        # 标题
        summary_lines.append("# Surge规则文件汇总")
        summary_lines.append(f"生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        summary_lines.append("")

        # 添加jsDelivr说明
        summary_lines.append("## 关于jsDelivr")
        summary_lines.append("jsDelivr是一个免费的CDN服务，可以加速GitHub文件的访问。")
        summary_lines.append("官方网站: [https://www.jsdelivr.com/github](https://www.jsdelivr.com/github)")
        summary_lines.append("")

        # 快速下载表格
        summary_lines.append("## 快速下载")
        summary_lines.append("| 分类 | 文件名 | 规则数 | GitHub下载 | jsDelivr下载 |")
        summary_lines.append("|------|--------|--------|------------|--------------|")

        for category_name, rules in classified_rules.items():
            if category_name in generated_files:
                category_config = self.categories.get(category_name, {})
                category_display_name = category_config.get('name', category_name)
                filename = os.path.basename(generated_files[category_name])
                github_url, jsdelivr_url = self._generate_file_urls(generated_files[category_name])

                if github_url and jsdelivr_url:
                    summary_lines.append(f"| {category_display_name} | `{filename}` | {len(rules)} | [下载]({github_url}) | [下载]({jsdelivr_url}) |")

        summary_lines.append("")

        # 统计信息
        total_rules = sum(len(rules) for rules in classified_rules.values())
        summary_lines.append(f"## 总体统计")
        summary_lines.append(f"- 总规则数: {total_rules}")
        summary_lines.append(f"- 分类数: {len(classified_rules)}")
        summary_lines.append(f"- 生成文件数: {len(generated_files)}")
        summary_lines.append("")

        # 各分类详情
        summary_lines.append("## 分类详情")
        for category_name, rules in classified_rules.items():
            category_config = self.categories.get(category_name, {})
            category_display_name = category_config.get('name', category_name)
            description = category_config.get('description', '')

            summary_lines.append(f"### {category_display_name}")
            if description:
                summary_lines.append(f"- 描述: {description}")
            summary_lines.append(f"- 规则数: {len(rules)}")

            if category_name in generated_files:
                filename = os.path.basename(generated_files[category_name])
                summary_lines.append(f"- 文件: `{filename}`")

                # 添加GitHub和jsDelivr链接
                github_url, jsdelivr_url = self._generate_file_urls(generated_files[category_name])
                if github_url and jsdelivr_url:
                    summary_lines.append("- 下载链接:")
                    summary_lines.append(f"  - [GitHub原始文件]({github_url})")
                    summary_lines.append(f"  - [jsDelivr加速]({jsdelivr_url})")
                    summary_lines.append("- 直接链接:")
                    summary_lines.append(f"  - GitHub: `{github_url}`")
                    summary_lines.append(f"  - jsDelivr: `{jsdelivr_url}`")

            # 规则类型统计
            stats = self.calculate_stats(rules)
            for rule_type, count in sorted(stats.items()):
                if rule_type != 'TOTAL':
                    summary_lines.append(f"  - {rule_type}: {count}")

            summary_lines.append("")
        
        # 写入汇总文件
        summary_path = os.path.join(self.output_dir, "README.md")
        try:
            with open(summary_path, 'w', encoding='utf-8') as f:
                f.write('\n'.join(summary_lines))

            self.logger.info(f"生成汇总文件: {summary_path}")

        except Exception as e:
            self.logger.error(f"生成汇总文件时出错: {e}")

    def _generate_file_urls(self, file_path: str) -> tuple:
        """
        生成文件的GitHub和jsDelivr URL

        Args:
            file_path: 文件路径

        Returns:
            (github_url, jsdelivr_url) 元组
        """
        try:
            # 获取仓库配置
            repo_config = self.config.get('repository', {})
            owner = repo_config.get('owner')
            repo_name = repo_config.get('name')
            branch = repo_config.get('branch', 'main')

            if not owner or not repo_name:
                self.logger.warning("未配置GitHub仓库信息，跳过URL生成")
                return None, None

            # 获取相对于项目根目录的文件路径
            # file_path 通常是 rules/rule/xxx.list 的形式
            relative_path = os.path.relpath(file_path, '.')

            # 生成GitHub原始文件URL
            github_url = f"https://raw.githubusercontent.com/{owner}/{repo_name}/refs/heads/{branch}/{relative_path}"

            # 生成jsDelivr CDN URL
            jsdelivr_url = f"https://cdn.jsdelivr.net/gh/{owner}/{repo_name}@refs/heads/{branch}/{relative_path}"

            return github_url, jsdelivr_url

        except Exception as e:
            self.logger.error(f"生成文件URL时出错: {e}")
            return None, None
