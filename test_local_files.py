#!/usr/bin/env python3
"""
本地文件支持功能测试脚本
演示如何在config.yaml中配置本地文件数据源
"""

import os
import yaml
from src.collector import RuleCollector

def test_local_file_support():
    """测试本地文件支持功能"""
    print("🔧 本地文件支持功能测试")
    print("="*60)
    
    # 加载配置
    with open('config.yaml', 'r', encoding='utf-8') as f:
        config = yaml.safe_load(f)
    
    print("\n📁 检查本地文件:")
    local_files = []
    
    # 检查所有分类中的本地文件配置
    for category_name, category_config in config.get('categories', {}).items():
        sources = category_config.get('sources', [])
        for source in sources:
            if 'file' in source:
                file_path = source['file']
                source_name = source.get('name', file_path)
                local_files.append((category_name, file_path, source_name))
                
                # 检查文件是否存在
                if os.path.exists(file_path):
                    with open(file_path, 'r', encoding='utf-8') as f:
                        lines = f.readlines()
                        rule_count = len([line for line in lines if line.strip() and not line.startswith('#')])
                    print(f"  ✅ {category_name}: {file_path} ({rule_count} 条规则)")
                    print(f"      名称: {source_name}")
                else:
                    print(f"  ❌ {category_name}: {file_path} (文件不存在)")
    
    if not local_files:
        print("  ℹ️  未配置本地文件数据源")
        return
    
    print(f"\n📊 本地文件统计:")
    print(f"  - 配置的本地文件数: {len(local_files)}")
    print(f"  - 涉及的分类数: {len(set(item[0] for item in local_files))}")
    
    # 测试收集器
    print(f"\n🔍 测试收集器处理本地文件:")
    collector = RuleCollector(config)
    
    # 只测试包含本地文件的分类
    categories_with_local_files = set(item[0] for item in local_files)
    
    for category_name in categories_with_local_files:
        print(f"\n  测试分类: {category_name}")
        category_config = config['categories'][category_name]
        sources_config = category_config.get('sources', [])
        
        try:
            stats, rules = collector.collect_from_category_sources(category_name, sources_config)
            print(f"    ✅ 收集成功: {len(rules)} 条规则")
            
            # 显示规则类型统计
            for rule_type, count in sorted(stats.items()):
                if rule_type != 'TOTAL':
                    print(f"      - {rule_type}: {count}")
                    
        except Exception as e:
            print(f"    ❌ 收集失败: {e}")

def show_local_file_examples():
    """显示本地文件配置示例"""
    print("\n📝 本地文件配置示例:")
    print("="*60)
    
    example_config = """
# 在config.yaml中配置本地文件数据源的示例:

categories:
  telegram:
    name: "Telegram"
    description: "Telegram相关域名和IP"
    output_file: "telegram.list"
    sources:
      # URL数据源
      - url: "https://example.com/telegram.list"
      
      # 本地文件数据源
      - file: "custom/telegram.list"
        name: "Custom Telegram Rules"  # 可选的显示名称
      
      # 相对路径和绝对路径都支持
      - file: "/absolute/path/to/rules.list"
      - file: "relative/path/to/rules.list"

  ai_services:
    name: "AI Services"
    sources:
      - url: "https://example.com/ai.list"
      - file: "custom/ai_services.list"
        name: "My AI Rules"
"""
    
    print(example_config)
    
    print("📋 支持的配置字段:")
    print("  - file: 本地文件路径（必需）")
    print("  - name: 显示名称（可选，默认使用文件名）")
    print("  - url:  远程URL（与file字段二选一）")
    
    print("\n🔧 本地文件格式要求:")
    print("  - 使用标准的Surge规则格式")
    print("  - 支持注释行（以#开头）")
    print("  - 支持空行")
    print("  - 自动进行去重处理")

def show_current_local_files():
    """显示当前配置的本地文件"""
    print("\n📂 当前配置的本地文件:")
    print("="*60)
    
    # 检查custom目录
    custom_dir = "custom"
    if os.path.exists(custom_dir):
        print(f"  📁 {custom_dir}/ 目录内容:")
        for filename in sorted(os.listdir(custom_dir)):
            filepath = os.path.join(custom_dir, filename)
            if os.path.isfile(filepath) and filename.endswith('.list'):
                with open(filepath, 'r', encoding='utf-8') as f:
                    lines = f.readlines()
                    rule_count = len([line for line in lines if line.strip() and not line.startswith('#')])
                print(f"    📄 {filename}: {rule_count} 条规则")
                
                # 显示文件前几行
                print("      内容预览:")
                for i, line in enumerate(lines[:5]):
                    if line.strip():
                        print(f"        {line.rstrip()}")
                if len(lines) > 5:
                    print("        ...")
                print()
    else:
        print(f"  ℹ️  {custom_dir}/ 目录不存在")

if __name__ == '__main__':
    test_local_file_support()
    show_local_file_examples()
    show_current_local_files()
    
    print("\n🎯 使用建议:")
    print("  1. 将自定义规则文件放在 custom/ 目录下")
    print("  2. 使用标准的Surge规则格式")
    print("  3. 在config.yaml中配置file字段指向本地文件")
    print("  4. 可以混合使用URL和本地文件数据源")
    print("  5. 本地文件会与远程规则一起进行去重处理")
    
    print("\n✨ 本地文件支持功能已就绪！")
